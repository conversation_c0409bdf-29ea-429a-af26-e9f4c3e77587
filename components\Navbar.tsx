import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import {
  HomeIcon,
  ChartBarIcon,
  Bars3Icon,
  PlusIcon,
  CalendarIcon,
  DocumentTextIcon
} from '../constants';
import UserDropdown from './UserDropdown';
import NotificationDropdown from './NotificationDropdown';
import MobileMenu from './MobileMenu';
import { useNotifications } from '../hooks/useNotifications';

interface NavbarProps {
  onAddCategory: () => void;
  onAddTransaction?: () => void;
  currentSection?: 'dashboard' | 'categories' | 'reports' | 'planning' | 'history';
  onSectionChange?: (section: 'dashboard' | 'categories' | 'reports' | 'planning' | 'history') => void;
}

const Navbar: React.FC<NavbarProps> = ({
  onAddCategory,
  onAddTransaction,
  currentSection = 'dashboard',
  onSectionChange
}) => {
  const { user } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { notifications } = useNotifications();

  const navItems = [
    { id: 'dashboard', label: 'Dashboard', icon: HomeIcon, href: '#dashboard' },
    { id: 'categories', label: 'Categories', icon: ChartBarIcon, href: '#categories' },
    { id: 'planning', label: 'Planning', icon: CalendarIcon, href: '#planning' },
    { id: 'history', label: 'History', icon: DocumentTextIcon, href: '#history' },
    { id: 'reports', label: 'Reports', icon: ChartBarIcon, href: '#reports' },
  ];

  const handleSectionClick = (sectionId: string) => {
    if (onSectionChange && (sectionId === 'dashboard' || sectionId === 'categories' || sectionId === 'reports' || sectionId === 'planning' || sectionId === 'history')) {
      onSectionChange(sectionId);
    }
  };

  if (!user) return null;

  return (
    <>
      {/* Main Navbar */}
      <nav className="bg-slate-800/95 backdrop-blur-sm border-b border-slate-700/50 sticky top-0 z-50 shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">

            {/* Left Section - Logo & Navigation */}
            <div className="flex items-center space-x-8">
              {/* Logo */}
              <div className="flex items-center space-x-3 flex-shrink-0">
                <div className="w-9 h-9 bg-gradient-to-br from-sky-400 to-sky-600 rounded-xl flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-sm">BP</span>
                </div>
                <div className="hidden sm:block">
                  <h1 className="text-xl font-bold text-white tracking-tight">Budget Planner</h1>
                  <p className="text-xs text-slate-400 -mt-0.5">Financial Control</p>
                </div>
              </div>

              {/* Desktop Navigation */}
              <div className="hidden md:flex items-center space-x-1">
                {navItems.map((item) => {
                  const Icon = item.icon;
                  const isActive = currentSection === item.id;
                  return (
                    <button
                      key={item.id}
                      onClick={() => handleSectionClick(item.id)}
                      className={`flex items-center space-x-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-200 ${
                        isActive
                          ? 'bg-sky-600 text-white shadow-lg shadow-sky-600/25 scale-105'
                          : 'text-slate-300 hover:text-white hover:bg-slate-700/50 hover:scale-105'
                      }`}
                    >
                      <Icon className="w-4 h-4" />
                      <span>{item.label}</span>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Right Section - Actions & User */}
            <div className="flex items-center space-x-3">

              {/* Quick Add Buttons */}
              <div className="hidden sm:flex items-center space-x-2">
                <button
                  onClick={onAddCategory}
                  className="flex items-center space-x-2 bg-sky-600 hover:bg-sky-700 text-white px-3 py-2.5 rounded-xl text-sm font-medium transition-all duration-200 shadow-lg shadow-sky-600/25 hover:scale-105"
                >
                  <PlusIcon className="w-4 h-4" />
                  <span className="hidden lg:inline">Category</span>
                </button>

                {onAddTransaction && (
                  <button
                    onClick={onAddTransaction}
                    className="flex items-center space-x-2 bg-emerald-600 hover:bg-emerald-700 text-white px-3 py-2.5 rounded-xl text-sm font-medium transition-all duration-200 shadow-lg shadow-emerald-600/25 hover:scale-105"
                  >
                    <PlusIcon className="w-4 h-4" />
                    <span className="hidden lg:inline">Transaction</span>
                  </button>
                )}
              </div>

              {/* Divider */}
              <div className="hidden sm:block w-px h-6 bg-slate-600"></div>

              {/* Notifications */}
              <NotificationDropdown notifications={notifications} />

              {/* User Dropdown */}
              <UserDropdown user={user} />

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMobileMenuOpen(true)}
                className="md:hidden p-2 text-slate-400 hover:text-white hover:bg-slate-700/50 rounded-xl transition-all duration-200 hover:scale-105"
                aria-label="Open menu"
              >
                <Bars3Icon className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

      </nav>

      {/* Mobile Menu */}
      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        navItems={navItems}
        currentSection={currentSection}
        onSectionChange={handleSectionClick}
        onAddCategory={onAddCategory}
        onAddTransaction={onAddTransaction}
      />
    </>
  );
};

export default Navbar;
